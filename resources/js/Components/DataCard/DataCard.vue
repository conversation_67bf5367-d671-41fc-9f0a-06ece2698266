<template>
    <div
        :class="[
            'rounded-[20px] border border-border-action bg-surface-action-3x-light p-24',
            textAlignClass
        ]"
    >
        <h3 class="mb-24 font-bold text-text-grey">
            <Tooltip
                v-if="tooltip"
                :label="tooltip.label"
                :tooltip="tooltip.tooltip"
            />
            <template v-else>
                {{ title }}
            </template>
        </h3>
        <p class="fs-md lg:fs-3xl">
            {{ data }}
        </p>
    </div>
</template>

<script setup>
import { computed } from 'vue'
import Tooltip from "../Tooltip/Tooltip.vue";

const props = defineProps({
    title: {
        type: String,
        required: true,
    },
    data: {
        type: [String, Number],
        required: true,
    },
    tooltip: {
        type: String,
        default: "",
    },
    textAlign: {
        type: String,
        default: 'center',
        validator: (value) => ['left', 'center', 'right'].includes(value)
    },
});

/**
 * Computed property to get the appropriate Tailwind text alignment class
 */
const textAlignClass = computed(() => {
    switch (props.textAlign) {
        case 'left':
            return 'text-left'
        case 'right':
            return 'text-right'
        case 'center':
        default:
            return 'text-center'
    }
})
</script>
